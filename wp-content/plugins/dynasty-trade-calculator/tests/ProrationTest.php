<?php

use PHPUnit\Framework\TestCase;

/**
 * Test proration behavior for upgrades and downgrades
 * 
 * This test verifies that:
 * 1. Proration is disabled for downgrades (returns 0)
 * 2. Proration is allowed for upgrades (returns original amount)
 * 3. Expiration length is adjusted correctly for downgrades
 */
class ProrationTest extends TestCase
{
    private $membership;

    protected function setUp(): void
    {
        // Initialize the membership class
        $this->membership = new \DynastyTradeCalculator\Membership();
    }

    /**
     * Test that proration is disabled for downgrades
     */
    public function testProrationDisabledForDowngrades()
    {
        // Mock membership objects for downgrade scenario
        $oldMembership = $this->createMockMembership(12, 'standard_200'); // $9.99 tier
        $newMembership = $this->createMockMembership(7, 'standard_50');   // $4.99 tier
        
        // Set up the new membership to indicate it was an upgrade (downgrade in our logic)
        $newMembership->method('was_upgrade')->willReturn(true);
        $newMembership->method('get_upgraded_from')->willReturn(1); // Mock old membership ID
        
        // Test the proration filter
        $originalDiscount = 15.50; // Some proration amount
        $result = $this->membership->disableProrationForDowngrades(
            $originalDiscount, 
            2, // Mock membership ID
            $newMembership
        );
        
        // For downgrades, proration should be disabled (return 0)
        $this->assertEquals(0, $result, 'Proration should be disabled for downgrades');
    }

    /**
     * Test that proration is allowed for upgrades
     */
    public function testProrationAllowedForUpgrades()
    {
        // Mock membership objects for upgrade scenario
        $oldMembership = $this->createMockMembership(7, 'standard_50');   // $4.99 tier
        $newMembership = $this->createMockMembership(12, 'standard_200'); // $9.99 tier
        
        // Set up the new membership to indicate it was an upgrade
        $newMembership->method('was_upgrade')->willReturn(true);
        $newMembership->method('get_upgraded_from')->willReturn(1); // Mock old membership ID
        
        // Test the proration filter
        $originalDiscount = 15.50; // Some proration amount
        $result = $this->membership->disableProrationForDowngrades(
            $originalDiscount, 
            2, // Mock membership ID
            $newMembership
        );
        
        // For upgrades, proration should be allowed (return original amount)
        $this->assertEquals($originalDiscount, $result, 'Proration should be allowed for upgrades');
    }

    /**
     * Test that proration is allowed for same subscription type
     */
    public function testProrationAllowedForSameSubscriptionType()
    {
        // Mock membership objects for same subscription type scenario
        $oldMembership = $this->createMockMembership(7, 'standard_50');
        $newMembership = $this->createMockMembership(7, 'standard_50'); // Same type
        
        // Set up the new membership to indicate it was an upgrade
        $newMembership->method('was_upgrade')->willReturn(true);
        $newMembership->method('get_upgraded_from')->willReturn(1); // Mock old membership ID
        
        // Test the proration filter
        $originalDiscount = 15.50; // Some proration amount
        $result = $this->membership->disableProrationForDowngrades(
            $originalDiscount, 
            2, // Mock membership ID
            $newMembership
        );
        
        // For same subscription type, proration should be allowed
        $this->assertEquals($originalDiscount, $result, 'Proration should be allowed for same subscription type');
    }

    /**
     * Test expiration length adjustment for downgrades
     */
    public function testExpirationLengthAdjustmentForDowngrades()
    {
        // Mock membership level
        $membershipLevel = $this->createMock(\RCP_Membership_Level::class);
        $membershipLevel->method('get_duration')->willReturn(30); // 30 days standard
        
        // Mock old membership for downgrade scenario
        $oldMembership = $this->createMockMembership(12, 'standard_200'); // $9.99 tier
        
        // Test the expiration length filter
        $proratedLength = 60; // RCP calculated 60 days due to proration
        $result = $this->membership->adjustExpirationLengthForDowngrades(
            $proratedLength,
            7, // New membership level ID (standard_50)
            1  // Old membership ID
        );
        
        // For downgrades, should return standard length, not prorated length
        $this->assertEquals(30, $result, 'Expiration length should be standard duration for downgrades, not prorated');
    }

    /**
     * Test expiration length preserved for upgrades
     */
    public function testExpirationLengthPreservedForUpgrades()
    {
        // Mock membership level
        $membershipLevel = $this->createMock(\RCP_Membership_Level::class);
        $membershipLevel->method('get_duration')->willReturn(30); // 30 days standard
        
        // Mock old membership for upgrade scenario
        $oldMembership = $this->createMockMembership(7, 'standard_50'); // $4.99 tier
        
        // Test the expiration length filter
        $proratedLength = 60; // RCP calculated 60 days due to proration
        $result = $this->membership->adjustExpirationLengthForDowngrades(
            $proratedLength,
            12, // New membership level ID (standard_200)
            1   // Old membership ID
        );
        
        // For upgrades, should preserve prorated length
        $this->assertEquals($proratedLength, $result, 'Expiration length should be preserved for upgrades');
    }

    /**
     * Test that pending downgrade memberships get correct expiration dates
     */
    public function testPendingDowngradeExpirationDate()
    {
        // Test scenario: Current membership expires July 25, 2025
        // Downgrade to 1-month plan should expire August 25, 2025

        $currentExpiration = '2025-07-25 23:59:59';
        $expectedNewExpiration = '2025-08-25 23:59:59';

        // Mock membership level (1 month duration)
        $membershipLevel = $this->createMock(\RCP_Membership_Level::class);
        $membershipLevel->method('get_duration')->willReturn(1);
        $membershipLevel->method('get_duration_unit')->willReturn('month');

        // Calculate what our code should produce
        $calculatedExpiration = date('Y-m-d H:i:s', strtotime($currentExpiration . ' + 1 month'));

        $this->assertEquals($expectedNewExpiration, $calculatedExpiration,
            'Pending downgrade should expire 1 month after current membership expires');
    }

    /**
     * Test expiration calculation for different membership durations
     */
    public function testExpirationCalculationForDifferentDurations()
    {
        $testCases = [
            // 1 month membership
            [
                'current_expiration' => '2025-07-25 23:59:59',
                'duration' => 1,
                'unit' => 'month',
                'expected' => '2025-08-25 23:59:59'
            ],
            // 3 month membership
            [
                'current_expiration' => '2025-07-25 23:59:59',
                'duration' => 3,
                'unit' => 'month',
                'expected' => '2025-10-25 23:59:59'
            ],
            // 1 year membership
            [
                'current_expiration' => '2025-07-25 23:59:59',
                'duration' => 1,
                'unit' => 'year',
                'expected' => '2026-07-25 23:59:59'
            ]
        ];

        foreach ($testCases as $case) {
            $calculated = date('Y-m-d H:i:s', strtotime($case['current_expiration'] . ' + ' . $case['duration'] . ' ' . $case['unit']));
            $this->assertEquals($case['expected'], $calculated,
                "Expiration calculation failed for {$case['duration']} {$case['unit']} duration");
        }
    }

    /**
     * Helper method to create mock membership objects
     */
    private function createMockMembership($levelId, $subscriptionType)
    {
        $membership = $this->createMock(\RCP_Membership::class);
        $membership->method('get_object_id')->willReturn($levelId);

        // Mock the getRotoGptSubscription method behavior
        $this->membership = $this->getMockBuilder(\DynastyTradeCalculator\Membership::class)
            ->onlyMethods(['getRotoGptSubscription'])
            ->getMock();

        $this->membership->method('getRotoGptSubscription')
            ->willReturn($subscriptionType);

        return $membership;
    }

    /**
     * Test the new early proration disable method for downgrades
     */
    public function testDisableProrationCreditForDowngrades()
    {
        // Mock membership objects for downgrade scenario
        $oldMembership = $this->createMockMembership(12, 'standard_200'); // $9.99 tier
        $newMembership = $this->createMockMembership(7, 'standard_50');   // $4.99 tier

        // Set up the new membership to indicate it was an upgrade (downgrade in our logic)
        $newMembership->method('was_upgrade')->willReturn(true);
        $newMembership->method('get_upgraded_from')->willReturn(1); // Mock old membership ID

        // Test the early proration disable filter
        $result = $this->membership->disableProrationCreditForDowngrades(false, $newMembership);

        // For downgrades, proration should be disabled entirely (return true)
        $this->assertTrue($result, 'Proration credit calculation should be disabled entirely for downgrades');
    }

    /**
     * Test the early proration disable method allows proration for upgrades
     */
    public function testDisableProrationCreditAllowedForUpgrades()
    {
        // Mock membership objects for upgrade scenario
        $oldMembership = $this->createMockMembership(7, 'standard_50');   // $4.99 tier
        $newMembership = $this->createMockMembership(12, 'standard_200'); // $9.99 tier

        // Set up the new membership to indicate it was an upgrade
        $newMembership->method('was_upgrade')->willReturn(true);
        $newMembership->method('get_upgraded_from')->willReturn(1); // Mock old membership ID

        // Test the early proration disable filter
        $result = $this->membership->disableProrationCreditForDowngrades(false, $newMembership);

        // For upgrades, proration should be allowed (return false)
        $this->assertFalse($result, 'Proration credit calculation should be allowed for upgrades');
    }
}
